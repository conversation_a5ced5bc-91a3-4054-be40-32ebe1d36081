package com.taktile.client

import co.highbeam.client.HighbeamHttpClientRequestBuilder
import co.highbeam.client.HttpResponse
import co.highbeam.client.RequestBuilder
import co.highbeam.protectedString.ProtectedString
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import mu.KotlinLogging

internal class TaktileRequest(
  private val httpClient: TaktileHttpClient,
  private val apiKey: ProtectedString,
  private val clientId: String,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun request(
    httpMethod: HttpMethod,
    path: String,
    qp: Map<String, List<String>> = emptyMap(),
    body: Any? = null,
    token: RequestBuilder = { useTaktileApiKey() },
  ): HttpResponse {
    logger.info { "Taktile request: [httpMethod=$httpMethod, path=$path, qp=$qp, body=$body]" }
    return httpClient.request(
      httpMethod = httpMethod,
      path = path,
      qp = qp,
      body = body,
      builder = token,
    )
  }

  private fun HighbeamHttpClientRequestBuilder.useTaktileApiKey() {
    putHeader(HttpHeaders.Authorization, "Bearer ${apiKey.value}")
    putHeader("x-client-id", clientId)
  }
}
