app:
  appBaseUrl: http://test.highbeam.co
  apiBaseUrl: http://test.api.highbeam.co

highbeamDatabase:
  jdbcUrl:
    type: Plaintext
    value: *****************************************
  username:
    type: EnvironmentVariable
    name: HIG<PERSON><PERSON>AM_TEST_POSTGRES_USERNAME
    defaultValue: highbeam
  password:
    type: EnvironmentVariable
    name: HIGH<PERSON>AM_TEST_POSTGRES_PASSWORD
  runMigrations: true
  connectionTimeout: 1000
  maximumPoolSize: 2

email:
  enabled: false

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: line-of-credit-accepted/test/slack-url
  requestBankVerificationLetterSlackWebhookPath: request-bank-verification-letter/test/slack-url
  lineOfCreditApprovalProcessedSlackWebhookPath: line-of-credit-approval-processed/test/slack-url
  lineOfCreditGenericMessageSlackWebhookPath: line-of-credit-generic-message/test/slack-url
  lineOfCreditAppSubmittedSlackWebhookPath: line-of-credit-app-submitted/test/slack-url
  highbeamSpvCollectionUnitAccountId: 421321
  highbeamOldFundingUnitAccountId: 421321
  highbeamThreadLineOfCreditCounterpartyId: 123124
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Sandbox: 4133904

chargeCard:
  chargeCardWebhookPath: charge-card/test/slack-url
  highbeamThreadChargeCardRepaymentUnitAccountId: 421321
  highbeamSpvCollectionUnitAccountId: 421321

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 421321
  highbeamSpvCollectionUnitAccountId: 421321
  highbeamOldFundingUnitAccountId: 421321
  oldFundingAccounts:
    - 421321
  capitalDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  capitalDrawdownApprovalWebhookPath: line-of-credit-drawdown/test/slack-url
  capitalExternalLenderDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  externalLenderLineOfCreditUnitAccountId:
    Sandbox: 4133904
  loanTapeSubledgerUnitAccountIds:
    - 123123

taktile:
  environment: Sandbox
  apiKey:
    type: Plaintext
    value: test-api-key
  clientId:
    type: Plaintext
    value: test-client-id
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

task:
  enabled: false
  projectName: fake
  locationName: fake
  cloudSchedulerRequestEmail: fake
  cloudSchedulerRequestAudience: fake
  cloudTasksToken:
    type: Plaintext
    value: test
