rootProject.name = "highbeam-backend"

// Common
include("common:auth0")
include("common:aws")
include("common:backend-v2")
include("common:client")
include("common:config")
include("common:darb")
include("common:email")
include("common:email:testing")
include("common:event")
include("common:event:testing")
include("common:exceptions")
include("common:feature")
include("common:feature:feature-flags-testing")
include("common:google-cloud-storage")
include("common:google-sheets")
include("common:hashing")
include("common:integration-testing")
include("common:job-runner")
include("common:job-runner:testing")
include("common:jwt")
include("common:metrics")
include("common:notion")
include("common:parquet")
include("common:parquet:testing")
include("common:permissions")
include("common:reps")
include("common:rest-feature")
include("common:rest-feature:testing")
include("common:rest-interface")
include("common:rutter")
include("common:sentry-feature")
include("common:serialization")
include("common:server")
include("common:shopify")
include("common:slack")
include("common:sql")
include("common:sql:testing")
include("common:taktile")
include("common:task")
include("common:task:testing")
include("common:type:date")
include("common:type:money")
include("common:type:optional")
include("common:type:protected-string")
include("common:type:slug")
include("common:type-conversion:implementation")
include("common:type-conversion:interface")
include("common:unit-co")
include("common:util")
include("common:validation")

// Databases
include("db:highbeam")

// Modules
include("module:backend-v2-email:client")
include("module:backend-v2-email:interface")
include("module:backend-v2-email:module")
include("module:business:business:client")
include("module:business:business:interface")
include("module:business:business:module")
include("module:business:details:client")
include("module:business:details:interface")
include("module:business:details:module")
include("module:connect:client")
include("module:connect:interface")
include("module:connect:module")
include("module:connect:util")
include("module:capital:capital-reporting:client")
include("module:capital:capital-reporting:interface")
include("module:capital:capital-reporting:module")
include("module:capital:charge-card:client")
include("module:capital:charge-card:interface")
include("module:capital:charge-card:module")
include("module:capital:charge-card:testing")
include("module:capital:account:client")
include("module:capital:account:interface")
include("module:capital:account:module")
include("module:capital:account:testing")
include("module:capital:credit:client")
include("module:capital:credit:interface")
include("module:capital:credit:module")
include("module:capital:onboarding:client")
include("module:capital:onboarding:interface")
include("module:capital:onboarding:module")
include("module:capital:transaction:client")
include("module:capital:transaction:interface")
include("module:capital:transaction:module")
include("module:capital:transaction:testing")
include("module:capital:treasury:module")
include("module:capital:repayment:client")
include("module:capital:repayment:interface")
include("module:capital:repayment:module")
include("module:capital:repayment:testing")
include("module:capital:repayment-schedule:client")
include("module:capital:repayment-schedule:module")
include("module:capital:repayment-schedule:interface")
include("module:evaluations:client")
include("module:evaluations:interface")
include("module:evaluations:module")
include("module:health-check:client")
include("module:health-check:interface")
include("module:health-check:module")
include("module:highbeam:client")
include("module:highbeam:interface")
include("module:highbeam:module")
include("module:intercom:client")
include("module:intercom:interface")
include("module:intercom:module")
include("module:job:client")
include("module:job:interface")
include("module:job:module")
include("module:metrics:client")
include("module:metrics:interface")
include("module:metrics:module")
include("module:onboarding:client")
include("module:onboarding:interface")
include("module:onboarding:module")
include("module:payment:gateway:client")
include("module:payment:gateway:interface")
include("module:payment:gateway:module")
include("module:transfer:client")
include("module:transfer:interface")
include("module:transfer:module")
include("module:treasury:client")
include("module:treasury:interface")
include("module:treasury:module")
include("module:user:client")
include("module:user:interface")
include("module:user:module")

// Servers
include("server:backend")
include("server:job-runner")
