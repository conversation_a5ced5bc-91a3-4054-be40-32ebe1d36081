app:
  appBaseUrl: http://localhost:3000
  apiBaseUrl: http://localhost:8080

auth0:
  baseUrl: https://highbeam-staging.us.auth0.com
  clientId: StQ9rKQsan7ty0CPCEB43QEuL248RkIO
  clientSecret:
    type: Plaintext
    value: ****************************************************************

aws: { }

backendV2:
  baseUrl: disabled
  jwtMechanism:
    source: Static
    issuer: test
    algorithm: Hmac256
    secret:
      type: Plaintext
      value: test

bankingConfig:
  slackWebhooks:
    monitoring: eng-monitoring-banking-slack-url
  promotionAprilReferralGuids: []

businessMetrics:
  enabled: false

clock:
  type: Fixed

fraudMonitor:
  cardAuthorizationSlackWebhookPath: test/slack-url
  cardStatusSlackWebhookPath: test/slack-url

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: line-of-credit-accepted/test/slack-url
  lineOfCreditApprovalProcessedSlackWebhookPath: line-of-credit-approval-processed/test/slack-url
  requestBankVerificationLetterSlackWebhookPath: request-bank-verification-letter/test/slack-url
  lineOfCreditGenericMessageSlackWebhookPath: line-of-credit-generic-message/test/slack-url
  lineOfCreditAppSubmittedSlackWebhookPath: line-of-credit-app-submitted/test/slack-url
  highbeamSpvCollectionUnitAccountId: 123124
  highbeamOldFundingUnitAccountId: 123124
  highbeamThreadLineOfCreditCounterpartyId: 123124
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Sandbox: 4133904

chargeCard:
  chargeCardWebhookPath: charge-card/test/slack-url
  highbeamThreadChargeCardRepaymentUnitAccountId: 123124
  highbeamSpvCollectionUnitAccountId: 123124

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 123124
  highbeamSpvCollectionUnitAccountId: 123124
  highbeamOldFundingUnitAccountId: 123124
  oldFundingAccounts:
    - 123124
  capitalDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  capitalDrawdownApprovalWebhookPath: line-of-credit-drawdown/test/slack-url
  capitalExternalLenderDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  externalLenderLineOfCreditUnitAccountId:
    Sandbox: 4133904
  loanTapeSubledgerUnitAccountIds:
    - 123123

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: Plaintext
    value: 4411b30708dbeb0d200d1da4086e30979427f7bb9ffa98697585d1689052df56
  webhookKey:
    type: Plaintext
    value: test-currency-cloud-webhook-key
  slackWebhookPath: testSlackWebhookPath
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

email:
  enabled: false

event:
  enabled:
    listening: false
    publishing: false

featureFlags:
  launchDarklySdkKey:
    type: Plaintext
    value: ****************************************

hashing:
  internalDataHashSecret:
    type: Plaintext
    value: highbeam

highbeamDatabase:
  jdbcUrl:
    type: Plaintext
    value: *****************************************
  username:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_USERNAME
    defaultValue: highbeam
  password:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_PASSWORD
  runMigrations: true
  connectionTimeout: 1000
  maximumPoolSize: 2

hosts:
  backend: http://localhost:8080

intercom:
  secret:
    type: Plaintext
  unitCoAdminUrl: https://app.s.unit.sh

jobsDatabase:
  jdbcUrl:
    type: Plaintext
    value: ************************************
  schema: quartz
  username:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_USERNAME
    defaultValue: highbeam
  password:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_PASSWORD
  startDelaySeconds: 0

metrics: { }

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: test-notion-database-id
  notionVersion: 2021-08-16
  secret:
    type: Plaintext
    value: test-secret

plaid:
  environment: Development
  webhookUrl: http://localhost:3000/plaid/webhook
  clientId: 6244724ca1f92500132352b6
  secret:
    type: Plaintext
    value: 3508b1b49b3d116af2dbd7599fae51

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            leeway: 300 # 5 minutes
            algorithm: Hmac256
            secret:
              type: Plaintext
              value: highbeam
      - type: Token
        internalToken:
          token:
            type: Plaintext
            value: internal-token
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: Plaintext
              value: cloud-tasks-token
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 6
    workerGroupSize: 12
    callGroupSize: 48

rutter:
  clientId:
    type: Plaintext
    value: test-client-id
  apiSecret:
    type: Plaintext
    value: test-api-secret
  baseUrl: https://sandbox.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: ''
  environment: ''

taktile:
  environment: Sandbox
  apiKey:
    type: Plaintext
    value: test-api-key
  clientId:
    type: Plaintext
    value: test-client-id
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

shopify:
  apiKey:
    type: Plaintext
    value: test-api-key
  apiSecret:
    type: Plaintext
    value: test-api-secret
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: http://localhost:8080/shopify-webhook
  mandatoryWebhookSlackWebhookPath: fake-mandatory-webhook-slack-webhook-path

task:
  enabled: false
  projectName: fake
  locationName: fake
  cloudSchedulerRequestEmail: fake
  cloudSchedulerRequestAudience: fake
  cloudTasksToken:
    type: Plaintext
    value: test

unitCo:
  environment: Sandbox
  secret:
    type: Plaintext
    value: v2.public.eyJyb2xlIjoiYWRtaW4iLCJ1c2VySWQiOiI5MjciLCJzdWIiOiJnYXV0YW1AaGlnaGJlYW0uY28iLCJleHAiOiIyMDIyLTExLTA4VDE0OjEzOjQxLjUzMFoiLCJqdGkiOiI4MzM1NSIsIm9yZ0lkIjoiNTUzIiwic2NvcGUiOiJhcHBsaWNhdGlvbnMgYXBwbGljYXRpb25zLXdyaXRlIGN1c3RvbWVycyBjdXN0b21lcnMtd3JpdGUgY3VzdG9tZXItdGFncy13cml0ZSBjdXN0b21lci10b2tlbi13cml0ZSBhY2NvdW50cyBhY2NvdW50cy13cml0ZSBjYXJkcyBjYXJkcy13cml0ZSBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyB0cmFuc2FjdGlvbnMtd3JpdGUgYXV0aG9yaXphdGlvbnMgc3RhdGVtZW50cyBwYXltZW50cyBwYXltZW50cy13cml0ZSBwYXltZW50cy13cml0ZS1jb3VudGVycGFydHkgcGF5bWVudHMtd3JpdGUtYWNoLWRlYml0IGNvdW50ZXJwYXJ0aWVzIGNvdW50ZXJwYXJ0aWVzLXdyaXRlIGJhdGNoLXJlbGVhc2VzIGJhdGNoLXJlbGVhc2VzLXdyaXRlIHdlYmhvb2tzIHdlYmhvb2tzLXdyaXRlIGV2ZW50cyBldmVudHMtd3JpdGUgYXV0aG9yaXphdGlvbi1yZXF1ZXN0cyBhdXRob3JpemF0aW9uLXJlcXVlc3RzLXdyaXRlIGNoZWNrLWRlcG9zaXRzIGNoZWNrLWRlcG9zaXRzLXdyaXRlIiwib3JnIjoiSGlnaGJlYW0iLCJzb3VyY2VJcCI6IiIsInVzZXJUeXBlIjoib3JnIn1pBjrDJ5RiPYZ-MtfYO7DWKpjlBwYFhAe2n3wZ2leWzMFoAhVeX2lt4HyVzFTBbiDUeiqZcCS6p3ekXAyUZZwD
  webhookToken:
    type: Plaintext
    value: xvzigaa5eb60uq2t0hp3gtypmcjtk8ulksg2m3q5qk6fg5jxiapvchqncuebpc15

uuids:
  generation: Deterministic
