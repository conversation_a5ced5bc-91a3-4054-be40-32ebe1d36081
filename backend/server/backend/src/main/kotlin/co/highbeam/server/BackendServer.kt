package co.highbeam.server

import co.highbeam.businessMetrics.BusinessMetricsFeature
import co.highbeam.capital.account.feature.CapitalAccountFeature
import co.highbeam.capital.chargeCard.feature.ChargeCardFeature
import co.highbeam.capital.repayment.feature.CapitalRepaymentFeature
import co.highbeam.capital.repaymentschedule.feature.CapitalRepaymentScheduleFeature
import co.highbeam.capital.reporting.feature.CapitalReportingFeature
import co.highbeam.capital.transaction.feature.CapitalTransactionFeature
import co.highbeam.config.ServerConfig
import co.highbeam.feature.AwsFeature
import co.highbeam.feature.FeatureFlagsFeature
import co.highbeam.feature.auth.AuthFeature
import co.highbeam.feature.backendV2.BackendV2Feature
import co.highbeam.feature.backendV2Email.BackendV2EmailFeature
import co.highbeam.feature.bankAccounts.BankAccountsFeature
import co.highbeam.feature.business.BusinessDetailsFeature
import co.highbeam.feature.business.BusinessFeature
import co.highbeam.feature.cards.CardsFeature
import co.highbeam.feature.clients.ClientsFeature
import co.highbeam.feature.connect.ConnectFeature
import co.highbeam.feature.credit.CreditFeature
import co.highbeam.feature.email.EmailFeature
import co.highbeam.feature.evaluations.EvaluationsFeature
import co.highbeam.feature.event.RealEventFeature
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorageFeature
import co.highbeam.feature.healthCheck.HealthCheckFeature
import co.highbeam.feature.intercom.IntercomFeature
import co.highbeam.feature.job.JobFeature
import co.highbeam.feature.jobRunner.JobRunnerFeature
import co.highbeam.feature.notion.NotionFeature
import co.highbeam.feature.onboarding.OnboardingFeature
import co.highbeam.feature.paymentV2.PaymentFeature
import co.highbeam.feature.rest.RestFeature
import co.highbeam.feature.sentry.SentryFeature
import co.highbeam.feature.sql.SqlFeature
import co.highbeam.feature.taktile.TaktileFeature
import co.highbeam.feature.transfer.TransferFeature
import co.highbeam.feature.treasury.TreasuryFeature
import co.highbeam.feature.unitCo.UnitCoFeature
import co.highbeam.feature.users.UserFeature
import co.highbeam.service.healthCheck.HealthCheckServiceImpl
import highbeam.feature.task.TaskFeature

internal class BackendServer(config: ServerConfig) : Server<ServerConfig>(config) {
  override val features = buildSet {
    add(AwsFeature(config.aws))
    add(BackendV2EmailFeature())
    add(
      ClientsFeature(
        auth0Config = config.auth0,
        hosts = config.hosts,
        notionConfig = config.notion,
        unitCoConfig = config.unitCo,
      )
    )
    add(EmailFeature(config.email))
    add(RealEventFeature(config.event))
    add(FeatureFlagsFeature(config.featureFlags))
    add(GoogleCloudStorageFeature(config.googleCloudStorage))
    add(HealthCheckFeature(HealthCheckServiceImpl::class))
    add(JobRunnerFeature(config.jobsDatabase, enabled = false))
    add(RestFeature(config.rest))
    add(SentryFeature(config.sentry))
    add(SqlFeature(config.highbeamDatabase))

    add(AuthFeature(config.rest.authentication.verifiers))
    add(BackendV2Feature(config.backendV2))
    add(
      BankAccountsFeature(
        bankingConfig = config.bankingConfig,
        currencyCloud = config.currencyCloud,
        plaid = config.plaid
      )
    )
    add(BusinessFeature())
    add(BusinessDetailsFeature())
    add(BusinessMetricsFeature(config.businessMetrics))
    add(CapitalReportingFeature())
    add(ConnectFeature(rutter = config.rutter, shopify = config.shopify))
    add(CapitalTransactionFeature(config.creditTransaction))
    add(CreditFeature(config.credit, config.taktile))
    add(CapitalAccountFeature())
    add(CapitalRepaymentFeature())
    add(CapitalRepaymentScheduleFeature())
    add(ChargeCardFeature(config.chargeCard))
    add(CardsFeature(config.fraudMonitor))
    add(EvaluationsFeature())
    add(IntercomFeature(config.intercom))
    add(JobFeature())
    add(NotionFeature(config.notion))
    add(OnboardingFeature())
    add(PaymentFeature())
    add(TaktileFeature(config.taktile))
    add(TaskFeature(config.task))
    add(TransferFeature(config.currencyCloud.webhookKey, config.currencyCloud.slackWebhookPath))
    add(TreasuryFeature())
    add(UnitCoFeature(config.unitCo))
    add(UserFeature())
  }
}
