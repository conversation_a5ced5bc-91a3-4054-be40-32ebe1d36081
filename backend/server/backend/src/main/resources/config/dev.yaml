# This config is used when running the app and database locally.
#
# No environment variables are required to use this config locally.

app:
  appBaseUrl: http://localhost:3000
  apiBaseUrl: http://localhost:8080

auth0:
  baseUrl: https://highbeam-staging.us.auth0.com
  clientId: StQ9rKQsan7ty0CPCEB43QEuL248RkIO
  clientSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_AUTH0_CLIENT_SECRET
    defaultValue: ****************************************************************

aws: { }

# Local config will really depend on how backend V2 is running,
# so this config is not expected to work out of the box.
backendV2:
  baseUrl: http://localhost:55100
  jwtMechanism:
    source: Static
    issuer: test
    algorithm: Hmac256
    secret:
      type: Plaintext
      value: test

bankingConfig:
  slackWebhooks:
    monitoring: triggers/T01GWSTDAN6/*************/a677a0eef03315a9f268739907523297
  promotionAprilReferralGuids: []

businessMetrics:
  enabled: false

clock:
  type: Real

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: workflows/line-of-credit-accepted/test/slack-url
  requestBankVerificationLetterSlackWebhookPath: request-bank-verification-letter/test/slack-url
  lineOfCreditApprovalProcessedSlackWebhookPath: workflows/line-of-credit-approval-processed/test/slack-url
  lineOfCreditGenericMessageSlackWebhookPath: workflows/line-of-credit-generic-message/test/slack-url
  lineOfCreditAppSubmittedSlackWebhookPath: workflows/line-of-credit-app-submitted/test/slack-url
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  highbeamThreadLineOfCreditCounterpartyId: 458356
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830

chargeCard:
  chargeCardWebhookPath: charge-card/test/slack-url
  highbeamThreadChargeCardRepaymentUnitAccountId: 1544691
  highbeamSpvCollectionUnitAccountId: 5645309

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 392395
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  oldFundingAccounts:
    - 1544691
  capitalDrawdownWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalDrawdownApprovalWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalExternalLenderDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830
  loanTapeSubledgerUnitAccountIds:
    - 5647300

email:
  enabled: false

event:
  enabled:
    listening: false
    publishing: false

featureFlags:
  launchDarklySdkKey:
    type: EnvironmentVariable
    name: HIGHBEAM_LAUNCH_DARKLY_SDK_KEY
    defaultValue: ****************************************

fraudMonitor:
  cardAuthorizationSlackWebhookPath: workflows/T01GWSTDAN6/A051TKMDAE7/******************/tXBiqg7lmhgPS1GxlUHTMf9d
  cardStatusSlackWebhookPath: workflows/T01GWSTDAN6/A051M253T9N/******************/BhF5SyBPLCvDlW4biUn6PjNg

googleCloudStorage:
  gcpProjectId: highbeam-staging
  trustedBucketName: assets.staging.highbeam.co
  untrustedBucketName: unscanned-assets.staging.highbeam.co
  internalBucketName: internal.staging.highbeam.co
  urlSigningDuration: 900 # 15 minutes

hashing:
  internalDataHashSecret:
    type: Plaintext
    value: highbeam

highbeamDatabase:
  jdbcUrl:
    type: Plaintext
    value: ************************************
  username:
    type: Plaintext
    value: highbeam
  password:
    type: Plaintext
  runMigrations: true
  connectionTimeout: 1000
  maximumPoolSize: 2

hosts:
  backend: http://localhost:8080

intercom:
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_INTERCOM_SECRET
  unitCoAdminUrl: https://app.s.unit.sh

jobsDatabase:
  jdbcUrl:
    type: Plaintext
    value: ************************************
  schema: quartz
  username:
    type: Plaintext
    value: highbeam
  password:
    type: Plaintext
  startDelaySeconds: 0

metrics: { }

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: cbcb2556907c47fcb1301306d6649dbe
  notionVersion: 2021-08-16
  secret:
    type: Plaintext
    value: this-secret-does-not-work

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: Plaintext
    value: 4411b30708dbeb0d200d1da4086e30979427f7bb9ffa98697585d1689052df56
  webhookKey:
    type: Plaintext
    value: invalid-key
  slackWebhookPath: workflows/T01GWSTDAN6/A042XFMG89M/426560728913163814/T8qEhY1apXgpahMjjJIr9Dot
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

plaid:
  environment: Development
  webhookUrl: http://localhost:3000/plaid/webhook
  clientId: 60b91c131c29f40010af12ec
  secret:
    type: Plaintext
    value: 17d2c778082e6b107ecd47d65ecd38
  enrichAccountsAsync: false

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            leeway: 300 # 5 minutes
            algorithm: Hmac256
            secret:
              type: Plaintext
              value: highbeam
          - source: Jwk
            issuer: https://auth.staging.highbeam.co/
            url: https://highbeam-staging.us.auth0.com/.well-known/jwks.json
          - source: Jwk
            issuer: accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
          - source: Jwk
            issuer: https://accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
      - type: Token
        internalToken:
          token:
            type: Plaintext
            value: internal-token
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: Plaintext
              value: super-secret-cloud-tasks-token
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 6
    workerGroupSize: 12
    callGroupSize: 48

# The Rutter integration won't work locally unless HIGHBEAM_RUTTER_CLIENT_ID and
# HIGHBEAM_RUTTER_API_SECRET are set. Visit https://dashboard.rutterapi.com to get real values
# and set them as environment variables.
rutter:
  clientId:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_CLIENT_ID
    defaultValue: dummy_client_id
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_API_SECRET
    defaultValue: dummy_api_secret
  baseUrl: https://production.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: ''
  environment: ''

# The Shopify integration won't work locally unless HIGHBEAM_SHOPIFY_API_KEY and
# HIGHBEAM_SHOPIFY_API_SECRET are set. Visit https://accounts.shopify.com to get real values
# and set them as environment variables.
shopify:
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_KEY
    defaultValue: dummy_api_key
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_SECRET
    defaultValue: dummy_api_secret
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: http://localhost:8080/shopify-webhook
  mandatoryWebhookSlackWebhookPath: workflows/fake-mandatory-webhook-slack-webhook-path

task:
  enabled: false
  projectName: fake
  locationName: fake
  cloudSchedulerRequestEmail: fake
  cloudSchedulerRequestAudience: fake
  cloudTasksToken:
    type: Plaintext
    value: test

taktile:
  environment: Sandbox
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_TAKTILE_API_KEY
    defaultValue: dummy_api_key
  clientId:
    type: EnvironmentVariable
    name: HIGHBEAM_TAKTILE_CLIENT_ID
    defaultValue: dummy_client_id
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

unitCo:
  environment: Sandbox
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_SECRET
    defaultValue: v2.public.eyJyb2xlIjoiYWRtaW4iLCJ1c2VySWQiOiIyMjAxIiwic3ViIjoic2l2YUBoaWdoYmVhbS5jbyIsImV4cCI6IjIwMjMtMTEtMTFUMTY6MjM6MjUuOTQ3WiIsImp0aSI6IjE5NTg4MCIsIm9yZ0lkIjoiNTUzIiwic2NvcGUiOiJhcHBsaWNhdGlvbnMgYXBwbGljYXRpb25zLXdyaXRlIGN1c3RvbWVycyBjdXN0b21lcnMtd3JpdGUgY3VzdG9tZXItdGFncy13cml0ZSBjdXN0b21lci10b2tlbi13cml0ZSBhY2NvdW50cyBhY2NvdW50cy13cml0ZSBjYXJkcyBjYXJkcy13cml0ZSBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyB0cmFuc2FjdGlvbnMtd3JpdGUgYXV0aG9yaXphdGlvbnMgc3RhdGVtZW50cyBwYXltZW50cyBwYXltZW50cy13cml0ZSBwYXltZW50cy13cml0ZS1jb3VudGVycGFydHkgcmVwYXltZW50cyByZXBheW1lbnRzLXdyaXRlIHBheW1lbnRzLXdyaXRlLWFjaC1kZWJpdCBjb3VudGVycGFydGllcyBjb3VudGVycGFydGllcy13cml0ZSBiYXRjaC1yZWxlYXNlcyBiYXRjaC1yZWxlYXNlcy13cml0ZSB3ZWJob29rcyB3ZWJob29rcy13cml0ZSBldmVudHMgZXZlbnRzLXdyaXRlIGF1dGhvcml6YXRpb24tcmVxdWVzdHMgYXV0aG9yaXphdGlvbi1yZXF1ZXN0cy13cml0ZSBjaGVjay1kZXBvc2l0cyBjaGVjay1kZXBvc2l0cy13cml0ZSByZWNlaXZlZC1wYXltZW50cyByZWNlaXZlZC1wYXltZW50cy13cml0ZSBkaXNwdXRlcyBjaGFyZ2ViYWNrcyBjaGFyZ2ViYWNrcy13cml0ZSByZXdhcmRzIHJld2FyZHMtd3JpdGUiLCJvcmciOiJIaWdoYmVhbSIsInNvdXJjZUlwIjoiIiwidXNlclR5cGUiOiJvcmciLCJpc1VuaXRQaWxvdCI6ZmFsc2V9l1MMCvR9UBiF3san3f23ms4gJ3Jg0hTnh42NwTar8zU-mNWqNTQf9CDz6EI6D-HpZ6NWpshs-yPzc_t7AQ9QDA
  webhookToken:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_WEBHOOK_TOKEN
    defaultValue: xvzigaa5eb60uq2t0hp3gtypmcjtk8ulksg2m3q5qk6fg5jxiapvchqncuebpc15

uuids:
  generation: Random
